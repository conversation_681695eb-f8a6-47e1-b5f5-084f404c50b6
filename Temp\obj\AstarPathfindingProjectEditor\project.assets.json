{"version": 3, "targets": {".NETStandard,Version=v2.1": {"AstarPathfindingProject/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/AstarPathfindingProject.dll": {}}, "runtime": {"bin/placeholder/AstarPathfindingProject.dll": {}}}, "PackageToolsEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AstarPathfindingProject": "1.0.0"}, "compile": {"bin/placeholder/PackageToolsEditor.dll": {}}, "runtime": {"bin/placeholder/PackageToolsEditor.dll": {}}}}}, "libraries": {"AstarPathfindingProject/1.0.0": {"type": "project", "path": "AstarPathfindingProject.csproj", "msbuildProject": "AstarPathfindingProject.csproj"}, "PackageToolsEditor/1.0.0": {"type": "project", "path": "PackageToolsEditor.csproj", "msbuildProject": "PackageToolsEditor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["AstarPathfindingProject >= 1.0.0", "PackageToolsEditor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj", "projectName": "AstarPathfindingProjectEditor", "projectPath": "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\AstarPathfindingProjectEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"h:\\Works\\TS\\AstarPathfindingProject.csproj": {"projectPath": "h:\\Works\\TS\\AstarPathfindingProject.csproj"}, "h:\\Works\\TS\\PackageToolsEditor.csproj": {"projectPath": "h:\\Works\\TS\\PackageToolsEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}}