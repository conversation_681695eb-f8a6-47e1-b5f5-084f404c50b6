{"version": 3, "targets": {".NETStandard,Version=v2.1": {"AstarPathfindingProject/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/AstarPathfindingProject.dll": {}}, "runtime": {"bin/placeholder/AstarPathfindingProject.dll": {}}}, "AstarPathfindingProjectEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AstarPathfindingProject": "1.0.0", "PackageToolsEditor": "1.0.0"}, "compile": {"bin/placeholder/AstarPathfindingProjectEditor.dll": {}}, "runtime": {"bin/placeholder/AstarPathfindingProjectEditor.dll": {}}}, "NaughtyAttributes.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/NaughtyAttributes.Core.dll": {}}, "runtime": {"bin/placeholder/NaughtyAttributes.Core.dll": {}}}, "NaughtyAttributes.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"NaughtyAttributes.Core": "1.0.0"}, "compile": {"bin/placeholder/NaughtyAttributes.Editor.dll": {}}, "runtime": {"bin/placeholder/NaughtyAttributes.Editor.dll": {}}}, "NaughtyAttributes.Test/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"NaughtyAttributes.Core": "1.0.0"}, "compile": {"bin/placeholder/NaughtyAttributes.Test.dll": {}}, "runtime": {"bin/placeholder/NaughtyAttributes.Test.dll": {}}}, "PackageToolsEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AstarPathfindingProject": "1.0.0"}, "compile": {"bin/placeholder/PackageToolsEditor.dll": {}}, "runtime": {"bin/placeholder/PackageToolsEditor.dll": {}}}, "Unity.RenderPipelines.HighDefinition.Config.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll": {}}}}}, "libraries": {"AstarPathfindingProject/1.0.0": {"type": "project", "path": "AstarPathfindingProject.csproj", "msbuildProject": "AstarPathfindingProject.csproj"}, "AstarPathfindingProjectEditor/1.0.0": {"type": "project", "path": "AstarPathfindingProjectEditor.csproj", "msbuildProject": "AstarPathfindingProjectEditor.csproj"}, "NaughtyAttributes.Core/1.0.0": {"type": "project", "path": "NaughtyAttributes.Core.csproj", "msbuildProject": "NaughtyAttributes.Core.csproj"}, "NaughtyAttributes.Editor/1.0.0": {"type": "project", "path": "NaughtyAttributes.Editor.csproj", "msbuildProject": "NaughtyAttributes.Editor.csproj"}, "NaughtyAttributes.Test/1.0.0": {"type": "project", "path": "NaughtyAttributes.Test.csproj", "msbuildProject": "NaughtyAttributes.Test.csproj"}, "PackageToolsEditor/1.0.0": {"type": "project", "path": "PackageToolsEditor.csproj", "msbuildProject": "PackageToolsEditor.csproj"}, "Unity.RenderPipelines.HighDefinition.Config.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.HighDefinition.Config.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.HighDefinition.Config.Runtime.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["AstarPathfindingProject >= 1.0.0", "AstarPathfindingProjectEditor >= 1.0.0", "NaughtyAttributes.Core >= 1.0.0", "NaughtyAttributes.Editor >= 1.0.0", "NaughtyAttributes.Test >= 1.0.0", "PackageToolsEditor >= 1.0.0", "Unity.RenderPipelines.HighDefinition.Config.Runtime >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "h:\\Works\\TS\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"h:\\Works\\TS\\AstarPathfindingProject.csproj": {"projectPath": "h:\\Works\\TS\\AstarPathfindingProject.csproj"}, "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj": {"projectPath": "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj"}, "h:\\Works\\TS\\NaughtyAttributes.Core.csproj": {"projectPath": "h:\\Works\\TS\\NaughtyAttributes.Core.csproj"}, "h:\\Works\\TS\\NaughtyAttributes.Editor.csproj": {"projectPath": "h:\\Works\\TS\\NaughtyAttributes.Editor.csproj"}, "h:\\Works\\TS\\NaughtyAttributes.Test.csproj": {"projectPath": "h:\\Works\\TS\\NaughtyAttributes.Test.csproj"}, "h:\\Works\\TS\\PackageToolsEditor.csproj": {"projectPath": "h:\\Works\\TS\\PackageToolsEditor.csproj"}, "h:\\Works\\TS\\Unity.RenderPipelines.HighDefinition.Config.Runtime.csproj": {"projectPath": "h:\\Works\\TS\\Unity.RenderPipelines.HighDefinition.Config.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}}