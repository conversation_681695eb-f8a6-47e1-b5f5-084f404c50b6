Using pre-set license
Built from '2020.3/staging' branch; Version is '2020.3.42f1 (7ade1201f527) revision 8052242'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 32607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
H:\Works\2020.3.42f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
H:/Works/TS
-logFile
Logs/AssetImportWorker0.log
-srvPort
55068
Successfully changed project path to: H:/Works/TS
H:/Works/TS
Using Asset Import Pipeline V2.
Refreshing native plugins compatible for Editor in 108.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.42f1 (7ade1201f527)
[Subsystems] Discovering subsystems at path H:/Works/2020.3.42f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/Works/TS/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 SUPER (ID=0x2783)
    Vendor:   
    VRAM:     11999 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'H:/Works/2020.3.42f1/Editor/Data/Managed'
Mono path[1] = 'H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56480
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.000735 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 103.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.502 seconds
Domain Reload Profiling:
	ReloadAssembly (502ms)
		BeginReloadAssembly (59ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (394ms)
			LoadAssemblies (59ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (100ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (19ms)
			SetupLoadedEditorAssemblies (196ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (104ms)
				BeforeProcessingInitializeOnLoad (8ms)
				ProcessInitializeOnLoadAttributes (59ms)
				ProcessInitializeOnLoadMethodAttributes (21ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.007555 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 102.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.353 seconds
Domain Reload Profiling:
	ReloadAssembly (1354ms)
		BeginReloadAssembly (105ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (16ms)
		EndReloadAssembly (1215ms)
			LoadAssemblies (108ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (296ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (65ms)
			SetupLoadedEditorAssemblies (687ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (103ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (464ms)
				ProcessInitializeOnLoadMethodAttributes (18ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (1ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.06 seconds
Refreshing native plugins compatible for Editor in 1.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6468 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 288.5 MB.
System memory in use after: 289.0 MB.

Unloading 45 unused Assets to reduce memory usage. Loaded Objects now: 6926.
Total: 3.554400 ms (FindLiveObjects: 0.310000 ms CreateObjectMapping: 0.153100 ms MarkObjects: 3.063300 ms  DeleteObjects: 0.027500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Resources/Data/Player/Alice.asset
  artifactKey: Guid(40ce26f1d82f30d4c9c7abde19141087) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/Player/Alice.asset using Guid(40ce26f1d82f30d4c9c7abde19141087) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6b5c08bc62e115700b4aa75c65d97283') in 0.037036 seconds 
========================================================================
Received Import Request.
  Time since last request: 63.874246 seconds.
  path: Assets/Scripts/Derivatives/FireField.cs
  artifactKey: Guid(3d83be5664a7b4741a53256a2e1f6aa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Derivatives/FireField.cs using Guid(3d83be5664a7b4741a53256a2e1f6aa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4a280bde5fc920ed751d3a2df4383484') in 0.008188 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011289 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.476 seconds
Domain Reload Profiling:
	ReloadAssembly (1476ms)
		BeginReloadAssembly (134ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (47ms)
		EndReloadAssembly (1307ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (340ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (84ms)
			SetupLoadedEditorAssemblies (631ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (109ms)
				ProcessInitializeOnLoadAttributes (505ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 270.1 MB.
System memory in use after: 270.6 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6930.
Total: 4.295000 ms (FindLiveObjects: 0.358000 ms CreateObjectMapping: 0.184200 ms MarkObjects: 3.720100 ms  DeleteObjects: 0.031600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 29.213902 seconds.
  path: Assets/Scripts/Derivatives/FireField.cs
  artifactKey: Guid(3d83be5664a7b4741a53256a2e1f6aa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Derivatives/FireField.cs using Guid(3d83be5664a7b4741a53256a2e1f6aa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd07a32ea0a37c8b237ad7bf7dde18ae9') in 0.007687 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000250 seconds.
  path: Assets/Resources/Prefabs/Derivative/Field/fireField.prefab
  artifactKey: Guid(6adb2b7e4cb9b3843b7dc3bba5cd061c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Derivative/Field/fireField.prefab using Guid(6adb2b7e4cb9b3843b7dc3bba5cd061c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e677fc3bccdb3e484ac7c9feff72378a') in 0.066940 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014565 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.146 seconds
Domain Reload Profiling:
	ReloadAssembly (1146ms)
		BeginReloadAssembly (124ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (40ms)
		EndReloadAssembly (987ms)
			LoadAssemblies (116ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (287ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (48ms)
			SetupLoadedEditorAssemblies (480ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (82ms)
				ProcessInitializeOnLoadAttributes (383ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.5 MB.
System memory in use after: 274.0 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6940.
Total: 3.580900 ms (FindLiveObjects: 0.350900 ms CreateObjectMapping: 0.168400 ms MarkObjects: 3.033400 ms  DeleteObjects: 0.027400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 37.862917 seconds.
  path: Assets/Scripts/Derivatives/FireField.cs
  artifactKey: Guid(3d83be5664a7b4741a53256a2e1f6aa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Derivatives/FireField.cs using Guid(3d83be5664a7b4741a53256a2e1f6aa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '66fe5f1354bc54bb2a353e9ef0c25a67') in 0.006326 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000200 seconds.
  path: Assets/Resources/Prefabs/Derivative/Field/fireField.prefab
  artifactKey: Guid(6adb2b7e4cb9b3843b7dc3bba5cd061c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Derivative/Field/fireField.prefab using Guid(6adb2b7e4cb9b3843b7dc3bba5cd061c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ee488a1d92620d7fce3bcc0c5006686c') in 0.036120 seconds 
