{"format": 1, "restore": {"h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj": {}}, "projects": {"h:\\Works\\TS\\AstarPathfindingProject.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\AstarPathfindingProject.csproj", "projectName": "AstarPathfindingProject", "projectPath": "h:\\Works\\TS\\AstarPathfindingProject.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\AstarPathfindingProject\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}, "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj", "projectName": "AstarPathfindingProjectEditor", "projectPath": "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\AstarPathfindingProjectEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"h:\\Works\\TS\\AstarPathfindingProject.csproj": {"projectPath": "h:\\Works\\TS\\AstarPathfindingProject.csproj"}, "h:\\Works\\TS\\PackageToolsEditor.csproj": {"projectPath": "h:\\Works\\TS\\PackageToolsEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}, "h:\\Works\\TS\\PackageToolsEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\PackageToolsEditor.csproj", "projectName": "PackageToolsEditor", "projectPath": "h:\\Works\\TS\\PackageToolsEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\PackageToolsEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"h:\\Works\\TS\\AstarPathfindingProject.csproj": {"projectPath": "h:\\Works\\TS\\AstarPathfindingProject.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}}}