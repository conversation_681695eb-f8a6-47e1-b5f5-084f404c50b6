using Werewolf.StatusIndicators.Components;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CharacterSkillSystem : MonoBehaviour 
{
    private CharacterSkillManager skillManager;
    private Animator animator;
    private SplatManager sm;

    private SkillData skill;

    int count;
    private void Start()
    {
        skillManager = GetComponent<CharacterSkillManager>();
        animator = GetComponent<Animator>();
        sm = GetComponent<SplatManager>();
        count = 0;
    }
    public void DeploySkill()
    {
        skillManager.GenerateSkill(skill);
    }
    public bool OpenSkillIndicator(int skillID)
    {
        skill = skillManager.PrepareSkill(skillID);
        if (skill == null || skill.skillIndicator == "")
        {
            Debug.Log("No skill indicator");
            return false;
        }
        else
        {
            Debug.Log("Open skill indicator");
            sm.SelectSpellIndicator(skill.skillIndicator);
            return true;
        }
    }
    public void CloseSkillIndicator()
    {
        sm.CancelSpellIndicator();
    }
    public void AttackUseSkill(int skillId)
    {
        //如果没有技能管理器，则获取技能管理器
        if (skillManager == null) { skillManager = GetComponent<CharacterSkillManager>();}

        //获取技能
        skill = skillManager.PrepareSkill(skillId);
        if (skill == null) { return; }
        if (skill.animationName != null & skill.animationName.Length > 0)
        {
            if (skill.animationName.Length != 1)
            {
                animator.Play(skill.animationName[count]);
                count += 1;
                if (count >= skill.animationName.Length)
                {
                    count = 0;
                }
            }
            else
            {
                animator.Play(skill.animationName[0]);
            }
        }
        DeploySkill();
    }
}



