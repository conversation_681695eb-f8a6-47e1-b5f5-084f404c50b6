﻿skillId,skillName,levelupBuffName,description,skillCd,cdRemain,skillDeployer,skillReleaseChecker,skillReleaseCheckerJson,attenuationType,coefficientType,attackCoeffient,CoeffientBaseTags+,filterTags,scriptName,propertyField,directionType,buffTypeId+,attachedProperty+,attackDistance,attackAngle,attackTargetTags+,impactType,attackType,scatterType,selectorType,skillIndicator,skillIconName,skillIcon,disappearType,durationTime,attackInterval,owner,prefabName,skillPrefab,animationName,hitFxName,hitFxPrefab,level,level
1,Skill - ShootIceBullectAfterDeath,LevelUp - ShootIceBullectAfterDeath,被击杀的敌人会向四周发射冰锥，冰锥会伤害并冻结命中的敌人,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,3,,,,,Oppsite,,Projectile/IceBullet,0.1,360,<PERSON>,<PERSON><PERSON><PERSON>,,Circle,Self,,,,TimeOver,,,,,,,,,,
3,Skill - TakeExtraDamageOnForzen,LevelUp - TakeExtraDamageOnForzen,被冻结的敌人受到的伤害提升,1,0,FollowMove,ObtainRequireBuff,{”requiredBuffTypeID”:”1002”},Linear,Constant,1,,,,,,2000,,,,Enemy,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
6,Skill - ConvertStacksToVenom,LevelUp - ConvertStacksToVenom,当敌人身上的中毒状态层数为10层时，消除该状态并使敌人获得一层猛毒,0,0,FollowMove,CheckBuffStackLayer,"{”requiredBuffTypeID”:”1008”&""LayerNum"":""10""}",Linear,Property,0.5,,,AttributeProperty,_validMaxHealth,,2001,,,,Enemy,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
7,Skill - RemovePosionAfterVenom,LevelUp - ConvertStacksToVenom,当敌人身上的中毒状态层数为10层时，消除该状态并使敌人获得一层猛毒,0,0,FollowMove,ObtainRequireBuff,{”requiredBuffTypeID”:”2001”},Linear,Constant,1,,,,,,1008,,,,Enemy,RemoveBuff,,,Self,,,,TimeOver,,,,,,,,,,
8,Skill - PassPosionLayerAfterDeath,LevelUp - PassPosionLayerAfterDeath,当处于中毒状态的敌人死亡时，使周围敌人获得1层中毒,0,0,FollowMove,CheckBuffStackLayer,"{”requiredBuffTypeID”:”1008”&""LayerNum"":""1""}",Linear,Constant,1,,,,,,1008,,1,360,Enemy,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
9,Skill - IncreaseLightningChainCountLevel1,LevelUp - IncreaseLightningChainCountLevel1,闪电的连锁次数提升至3,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,3,,,ScenceLog,MaxChainTargets,,,,,,Player,CoefficientModify,,,Self,,,,TimeOver,,,,,,,,,,
10,Skill - IncreaseLightningChainCountLevel2,LevelUp - IncreaseLightningChainCountLevel2,闪电的连锁次数提升至5,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,5,,,ScenceLog,MaxChainTargets,,,,,,Player,CoefficientModify,,,Self,,,,TimeOver,,,,,,,,,,
11,Skill - IncreaseLightningChainCountLevel3,LevelUp - IncreaseLightningChainCountLevel3,闪电的连锁次数提升至7,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,7,,,ScenceLog,MaxChainTargets,,,,,,Player,CoefficientModify,,,Self,,,,TimeOver,,,,,,,,,,
14,Skill - ReduceDamegeAfterHealLevel1,LevelUp - ReduceDamegeAfterHealLevel1,友方处于治疗状态时，受到的伤害小幅度减少,0,0,FollowMove,ObtainRequireBuff,{”requiredBuffTypeID”:”1005”},Linear,Constant,1,,,,,,2002,,,,Summor;Tower,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
15,Skill - ReduceDamegeAfterHealLevel2,LevelUp - ReduceDamegeAfterHealLevel2,友方处于治疗状态时，受到的伤害中幅度减少,0,0,FollowMove,ObtainRequireBuff,{”requiredBuffTypeID”:”1005”},Linear,Constant,0.9,,,,,,2003,,,,Summor;Tower,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
16,Skill - ReduceDamegeAfterHealLevel3,LevelUp - ReduceDamegeAfterHealLevel3,友方处于治疗状态时，受到的伤害大幅度减少,0,0,FollowMove,ObtainRequireBuff,{”requiredBuffTypeID”:”1005”},Linear,Constant,0.8,,,,,,2004,,,,Summor;Tower,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
17,Skill - ReflectDamageAfterHealLevel1,LevelUp - ReflectDamageAfterHealLevel1,处于治疗状态的友方被攻击后，将所受攻击伤害的10%反弹给附近的敌人,0,0,FollowMove,CheckBuffStackLayer,"{”requiredBuffTypeID”:”1005”&""LayerNum"":""1""}",Linear,Property,-0.1,,,AttributeProperty,LastHealthLoss,,,,1,360,1+N1+O13:V1340:S141,Damage,aoe,,Sector,,,,TimeOver,,,,,,,,,,
18,Skill - ReflectDamageAfterHealLevel2,LevelUp - ReflectDamageAfterHealLevel2,处于治疗状态的友方被攻击后，将所受攻击伤害的20%反弹给附近的敌人,0,0,FollowMove,CheckBuffStackLayer,"{”requiredBuffTypeID”:”1005”&""LayerNum"":""1""}",Linear,Property,-0.2,,,AttributeProperty,LastHealthLoss,,,,1,360,Enemy,Damage,aoe,,Sector,,,,TimeOver,,,,,,,,,,
19,Skill - ReflectDamageAfterHealLevel3,LevelUp - ReflectDamageAfterHealLevel3,处于治疗状态的友方被攻击后，将所受攻击伤害的30%反弹给附近的敌人,0,0,FollowMove,CheckBuffStackLayer,"{”requiredBuffTypeID”:”1005”&""LayerNum"":""1""}",Linear,Property,-0.3,,,AttributeProperty,LastHealthLoss,,,,1,360,Enemy,Damage,aoe,,Sector,,,,TimeOver,,,,,,,,,,
20,Skill - RangeDamageAfterOverHeal,LevelUp - RangeDamageAfterOverHeal,对满血的友方进行治疗，将对附近敌人造成相当于溢出治疗量50%的伤害,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.5,,,AttributeProperty,overHeal,,,,1,360,Enemy,Damage,aoe,,Sector,,,,TimeOver,,,,,,,,,,
21,Skill - ExtraMaxHealthAfterBless,LevelUp - ExtraMaxHealthAfterBless,处于祝福状态的友方会获得临时的额外生命,0,0,FollowMove,ObtainRequireBuff,{”requiredBuffTypeID”:”1007”},Linear,Constant,10,,,,,,2005,,,,Summor;Tower,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
25,Skill - IncreaseAllyDamageByEnemyUnderControlLevel1,LevelUp - IncreaseAllyDamageByEnemyUnderControlLevel1,依据处于控制状态的敌人数量小幅度提高友方攻击力,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,1,,,ScenceLog,EnemyUnderControl,,2006,,,,Summor;Tower;Player,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
26,Skill - IncreaseAllyDamageByEnemyUnderControlLevel2,LevelUp - IncreaseAllyDamageByEnemyUnderControlLevel2,依据处于控制状态的敌人数量中幅度提高友方攻击力,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,2,,,ScenceLog,EnemyUnderControl,,2006,,,,Summor;Tower;Player,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
27,Skill - IncreaseAllyDamageByEnemyUnderControlLevel3,LevelUp - IncreaseAllyDamageByEnemyUnderControlLevel3,依据处于控制状态的敌人数量大幅度提高友方攻击力,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,3,,,ScenceLog,EnemyUnderControl,,2006,,,,Summor;Tower;Player,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
30,Skill - LeaveFirePathAfterMove,LevelUp - LeaveFirePathAfterMove,玩家移动时会留下一个持续5秒的火焰路径，处于火焰路径上的敌人获得灼烧,1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,Derivative/Field/fireField,0,,,Summon,,,Self,,,,TimeOver,,,,,,,,,,
31,Skill - ConvertPlayerSpeedToAllyAttackDamage,LevelUp - ConvertPlayerSpeedToAllyAttackDamage,友方获得基于玩家移动速度的额外攻击力,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,1,,,PlayerController,_validMoveSpeed,,2007,,,,Summor;Tower;Player,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
32,Skill - CovertPlayerSpeedToPlayerEvasionRate,LevelUp - CovertPlayerSpeedToPlayerEvasionRate,玩家有几率闪避受到的攻击，移动速度越高闪避概率越高，最高为60%,5,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,PlayerController,_validMoveSpeed,,,,,,,CoefficientModify,,,Self,,,,TimeOver,,,,,,,,,,
33,Skill - GainExtraEXPByKillLevel1,LevelUp - GainExtraEXPByKillLevel1,敌方死亡时有小概率掉落双倍经验,0,0,FollowMove,ConstantProbability,{”probability”:”0.1”},Linear,Constant,1,,,,,,,Prefabs/Drops/ExpItem,,,Enemy,Summon,,,Self,,,,TimeOver,,,,,,,,,,
34,Skill - GainExtraEXPByKillLevel2,LevelUp - GainExtraEXPByKillLevel2,敌方死亡时有中概率掉落双倍经验,0,0,FollowMove,ConstantProbability,{”probability”:”0.2”},Linear,Constant,1,,,,,,,Prefabs/Drops/ExpItem,,,Enemy,Summon,,,Self,,,,TimeOver,,,,,,,,,,
35,Skill - GainExtraEXPByKillLevel3,LevelUp - GainExtraEXPByKillLevel3,敌方死亡时有大概率掉落双倍经验,0,0,FollowMove,ConstantProbability,{”probability”:”0.3”},Linear,Constant,1,,,,,,,Prefabs/Drops/ExpItem,,,Enemy,Summon,,,Self,,,,TimeOver,,,,,,,,,,
36,Skill - GainExtraAttributesAfterLevelUpLevel1,LevelUp - GainExtraAttributesAfterLevelUpLevel1,玩家每次升级永久获取少量攻击力,0,0,FollowMove,ConstantProbability,{”probability”:”0.3”},Linear,Constant,1,,,,,,2008,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
37,Skill - GainExtraAttributesAfterLevelUpLevel2,LevelUp - GainExtraAttributesAfterLevelUpLevel2,玩家每次升级永久获取中量攻击力,0,0,FollowMove,ConstantProbability,{”probability”:”0.3”},Linear,Constant,1,,,,,,2009,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
38,Skill - GainExtraAttributesAfterLevelUpLevel3,LevelUp - GainExtraAttributesAfterLevelUpLevel3,玩家每次升级永久获取大量攻击力,0,0,FollowMove,ConstantProbability,{”probability”:”0.3”},Linear,Constant,1,,,,,,2010,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
39,Skill - GainMaxHealthAfterLevelUpLevel1,LevelUp - GainMaxHealthAfterLevelUpLevel1,玩家每次升级有小概率1点额外生命上限,0,0,FollowMove,ConstantProbability,{”probability”:”0.1”},Linear,Constant,1,,,,,,2011,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
40,Skill - GainMaxHealthAfterLevelUpLevel2,LevelUp - GainMaxHealthAfterLevelUpLevel2,玩家每次升级有中概率1点额外生命上限,0,0,FollowMove,ConstantProbability,{”probability”:”0.2”},Linear,Constant,1,,,,,,2011,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
41,Skill - GainMaxHealthAfterLevelUpLevel3,LevelUp - GainMaxHealthAfterLevelUpLevel3,玩家每次升级有大概率1点额外生命上限,0,0,FollowMove,ConstantProbability,{”probability”:”0.3”},Linear,Constant,1,,,,,,2011,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
42,Skill - HealPlayerAfterKillLevel1,LevelUp - HealPlayerAfterKillLevel1,每当敌人死亡时，玩家有极小概率恢复生命,0,0,FollowMove,ConstantProbability,{”probability”:”0.01”},Linear,Constant,1,,,,,,2012,,10,360,Player,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
43,Skill - HealPlayerAfterKillLevel2,LevelUp - HealPlayerAfterKillLevel2,每当敌人死亡时，玩家有较小概率恢复生命,0,0,FollowMove,ConstantProbability,{”probability”:”0.03”},Linear,Constant,1,,,,,,2012,,10,360,Player,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
44,Skill - HealPlayerAfterKillLevel3,LevelUp - HealPlayerAfterKillLevel3,每当敌人死亡时，玩家有小概率恢复生命,0,0,FollowMove,ConstantProbability,{”probability”:”0.05”},Linear,Constant,1,,,,,,2012,,10,360,Player,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
45,Skill - IncreasePlayerMaxHealthAfterHurt,LevelUp - IncreasePlayerMaxHealthAfterHurt,玩家每次受到伤害时有概率获取1点生命上限,0,0,FollowMove,ConstantProbability,{”probability”:”0.5”},Linear,Constant,1,,,,,,2013,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
46,Skill - TransferDamageToAllyPart1,LevelUp - TransferDamageToAlly,玩家受到伤害时，若附近存在召唤物，则将该伤害转移给最近的召唤物,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Negtive,Property,1,,,PlayerController,LastHealthLoss,,,,1,360,Summor,Damage,single,,Sector,,,,TimeOver,,,,,,,,,,
47,Skill - TransferDamageToAllyPart2,LevelUp - TransferDamageToAlly,玩家受到伤害时，若附近存在召唤物，则将该伤害转移给最近的召唤物,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Negtive,Property,1,,,PlayerController,LastHealthLoss,,,,1,360,Summor,ConditionalSelfHeal,aoe,,Sector,,,,TimeOver,,,,,,,,,,
48,Skill - RevivePlayerAfterDeath,LevelUp - RevivePlayerAfterDeath,玩家死亡后复活并恢复初始血量的一半(每局游戏限一次),0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.5,,,PlayerController,maxHealth,,,,1,360,Player,Heal,single,,Self,,,,TimeOver,,,,,,,,,,
49,Skill - ConvertPlayerAttackSpeedToAllyAttackSpeedLevel1,LevelUp - ConvertPlayerAttackSpeedToAllyAttackSpeedLevel1,基于玩家攻击速度，小幅度提升所有友军攻击速度,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.5,,,PlayerController,_validAttackSpeed,,2014,,1,360,Summor;Tower,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
50,Skill - ConvertPlayerAttackSpeedToAllyAttackSpeedLevel2,LevelUp - ConvertPlayerAttackSpeedToAllyAttackSpeedLevel2,基于玩家攻击速度，中幅度提升所有友军攻击速度,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.6,,,PlayerController,_validAttackSpeed,,2015,,1,360,Summor;Tower,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
51,Skill - ConvertPlayerAttackSpeedToAllyAttackSpeedLevel3,LevelUp - ConvertPlayerAttackSpeedToAllyAttackSpeedLevel3,基于玩家攻击速度，大幅度提升所有友军攻击速度,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.7,,,PlayerController,_validAttackSpeed,,2016,,1,360,Summor;Tower,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
52,Skill - ConvertPlayerAttackDamageToAllyAttackDamageLevel1,LevelUp - ConvertPlayerAttackDamageToAllyAttackDamageLevel1,基于玩家攻击力，小幅度提升所有友军攻击力,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.5,,,PlayerController,_validAttackDamage,,2017,,1,360,Summor;Tower,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
53,Skill - ConvertPlayerAttackDamageToAllyAttackDamageLevel2,LevelUp - ConvertPlayerAttackDamageToAllyAttackDamageLevel2,基于玩家攻击力，中幅度提升所有友军攻击力,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.6,,,PlayerController,_validAttackDamage,,2018,,1,360,Summor;Tower,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
54,Skill - ConvertPlayerAttackDamageToAllyAttackDamageLevel3,LevelUp - ConvertPlayerAttackDamageToAllyAttackDamageLevel3,基于玩家攻击力，大幅度提升所有友军攻击力,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.7,,,PlayerController,_validAttackDamage,,2019,,1,360,Summor;Tower,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
55,Skill - ExtraDamageToBossLevel1,LevelUp - ExtraDamageToBossLevel1,小幅度提升Boss受到的所有伤害,-1,0,FollowMove,RequiredDeployerTag,{”requiredTag”:”Boss”},Linear,Constant,1,,,,,,2020,,,,Enemy,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
56,Skill - ExtraDamageToBossLevel2,LevelUp - ExtraDamageToBossLevel2,中幅度提升Boss受到的所有伤害,-1,0,FollowMove,RequiredDeployerTag,{”requiredTag”:”Boss”},Linear,Constant,1,,,,,,2021,,,,Enemy,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
57,Skill - ExtraDamageToBossLevel3,LevelUp - ExtraDamageToBossLevel3,大幅度提升Boss受到的所有伤害,-1,0,FollowMove,RequiredDeployerTag,{”requiredTag”:”Boss”},Linear,Constant,1,,,,,,2022,,,,Enemy,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
64,Skill - UnlockFireTower,LevelUp - UnlockFireTower,解锁建造火焰塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,65,,,,91,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
65,FireTower,LevelUp - UnlockFireTower,召唤一座火焰塔，火焰塔会向最近的敌人发射火球，造成伤害并使其灼烧,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
66,Skill - UnlockFireBallTower,LevelUp - UnlockFireBallTower,升级火焰塔为火球塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,67,,,,92,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
67,FireBallTower,LevelUp - UnlockFireBallTower,火焰塔发射的火球可以穿透敌人,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
68,Skill - UnlockFireJetTower,LevelUp - UnlockFireJetTower,升级火焰为扇形火焰,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,69,,,,93,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
69,FireJetTower,LevelUp - UnlockFireJetTower,火焰塔不再发射火球，改为喷射锥形的火焰，对接触的敌人造成造成伤害并使其灼烧,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
70,Skill - UnlockFireGiant,LevelUp - UnlockFireGiant,升级火焰塔为火巨人,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,71,,,,94,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
71,FireGiant,LevelUp - UnlockFireGiant,将你的火焰塔替换为火焰巨人，火焰巨人会攻击附近的敌人并使其灼烧,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
72,Skill - UnlockIceTower,LevelUp - UnlockIceTower,解锁建造冰霜塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,73,,,,95,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
73,IceTower,LevelUp - UnlockIceTower,召唤一座冰霜塔，冰霜塔会向最近的敌人发射冰锥，造成伤害并使其冻结,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
74,Skill - UnlockIceGiant,LevelUp - UnlockIceGiant,升级冰霜塔为冰霜巨人,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,75,,,,96,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
75,IceGiant,LevelUp - UnlockIceGiant,将你的冰霜塔替换为冰霜巨人，冰霜巨人会攻击附近的敌人并使其灼烧,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
76,Skill - UnlockBlizzardTower,LevelUp - UnlockBlizzardTower,升级冰霜塔为暴雪塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,77,,,,97,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
77,BlizzardTower,LevelUp - UnlockBlizzardTower,冰霜塔不再发射冰锥，改为召唤一小片暴风雪，对暴风雪中的敌人造成伤害并使其冻结,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
78,Skill - UnlockDarkTower,LevelUp - UnlockDarkTower,解锁建造暗影塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,79,,,,98,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
79,DarkTower,LevelUp - UnlockDarkTower,召唤一座暗影塔，暗影塔会周期性释放领域，吸附附近的敌人，造成伤害并使其致盲,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
80,Skill - UnlockBatTower,LevelUp - UnlockBatTower,升级暗影塔为蝙蝠塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,81,,,,99,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
81,BatTower,LevelUp - UnlockBatTower,暗影塔不再释放领域，改为召唤蝙蝠，蝙蝠会攻击周围敌人，造成伤害并使其致盲,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
82,Skill - UnlockDarkChargeTower,LevelUp - UnlockDarkChargeTower,升级暗影塔为黑洞,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,83,,,,100,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
83,DarkChargeTower,LevelUp - UnlockDarkChargeTower,暗影领域范围大幅扩大，并在领域结束时造成一次额外伤害，但不再吸附敌人,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
84,Skill - UnlockLightningTower,LevelUp - UnlockLightningTower,解锁建造闪电塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,85,,,,101,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
85,LightningTower,LevelUp - UnlockLightningTower,召唤一座闪电塔，闪电塔会向最近的敌人发射闪电并造成伤害,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
86,Skill - UnlockLightningBird,LevelUp - UnlockLightningBird,升级闪电塔为闪电鸟,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,87,,,,102,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
87,LightningBird,LevelUp - UnlockLightningBird,将你的闪电塔替换为闪电鸟，闪电鸟会向最近的敌人施放闪电,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
88,Skill - UnlockChainLightningTower,LevelUp - UnlockChainLightningTower,升级闪电塔为多发闪电塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,89,,,,103,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
89,ChainLightningTower,LevelUp - UnlockChainLightningTower,闪电塔每次攻击时额外发射一道闪电,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
90,Skill - UnlockWindTower,LevelUp - UnlockWindTower,解锁建造风之塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,91,,,,104,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
91,WindTower,LevelUp - UnlockWindTower,召唤一座风之塔，风之塔会对最近的敌人挂起旋风，旋风对沿途的敌人造成伤害，并留下风痕.风痕上的友方获得加速,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
92,Skill - UnlockStormTower,LevelUp - UnlockStormTower,升级风之塔为季风塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,93,,,,105,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
93,StormTower,LevelUp - UnlockStormTower,风之塔不再召唤旋风，改为召唤一个季风，季风会推开周围的敌人并造成伤害，处于季风内的友方获得加速,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
94,Skill - UnlockReinforceWindTower,LevelUp - UnlockReinforceWindTower,升级风之塔为旋风塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,95,,,,106,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
95,ReinforceWindTower,LevelUp - UnlockReinforceWindTower,旋风对命中的敌人造成减速效果,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
96,Skill - UnlockLightTower,LevelUp - UnlockLightTower,解锁建造光之塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,97,,,,107,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
97,LightTower,LevelUp - UnlockLightTower,召唤一座光之塔，光之塔会治疗最近的受伤友方单位,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
98,Skill - UnlockSacredTower,LevelUp - UnlockSacredTower,升级光之塔为治愈塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,99,,,,108,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
99,SacredTower,LevelUp - UnlockSacredTower,光之塔会治疗范围内所有友方,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
100,Skill - UnlockReinforceLightTower,LevelUp - UnlockReinforceLightTower,升级光之塔为祝福塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,101,,,,109,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
101,ReinforceLightTower,LevelUp - UnlockReinforceLightTower,光之塔不再治疗友方，改为祝福周围友方单位,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
102,Skill - UnlockWoodTower,LevelUp - UnlockWoodTower,解锁建造木之塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,103,,,,110,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
103,WoodTower,LevelUp - UnlockWoodTower,召唤一棵巨木，巨木会向周围的敌人释放毒气，造成伤害并使其中毒,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
104,Skill - UnlockVineTower,LevelUp - UnlockVineTower,升级木之塔为藤蔓塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,105,,,,111,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
105,VineTower,LevelUp - UnlockVineTower,巨木不再释放毒气，改为使用藤蔓攻击附近的敌人，造成伤害并使其定身,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
106,Skill - UnlockDryadTower,LevelUp - UnlockDryadTower,升级木之塔为巨木,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,107,,,,112,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
107,DryadTower,LevelUp - UnlockDryadTower,巨木不再释放毒气，改为召唤一颗树种，树种会向最近的敌人移动然后爆炸，对范围内敌人造成伤害并使其中毒,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
108,Skill - UnlockLaserTower,LevelUp - UnlockLaserTower,解锁建造激光,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,109,,,,113,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
109,LaserTower,LevelUp - UnlockLaserTower,召唤一座激光塔，持续攻击最近的敌人，伤害随攻击时间增加，转移目标后重置,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
110,Skill - UnlockLaserShockTower,LevelUp - UnlockLaserShockTower,升级激光塔为激光炮,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,111,,,,114,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
111,LaserShockTower,LevelUp - UnlockLaserShockTower,将激光改造为冲击波，周期性对直线上的所有目标造成巨额伤害,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
112,Skill - UnlockLaserCircleTower,LevelUp - UnlockLaserCircleTower,升级激光塔为激光陷阱,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,113,,,,115,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
113,LaserCircleTower,LevelUp - UnlockLaserCircleTower,将激光塔改造为旋转的激光陷阱，对激光命中的敌人造成伤害,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
114,Skill - RecoverTowerHealthLevel1,LevelUp - RecoverTowerHealthLevel1,小幅度提升防御塔的持续恢复效果,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,2023,,,,Tower,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
115,Skill - RecoverTowerHealthLevel2,LevelUp - RecoverTowerHealthLevel2,中幅度提升防御塔的持续恢复效果,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,2024,,,,Tower,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
116,Skill - RecoverTowerHealthLevel3,LevelUp - RecoverTowerHealthLevel3,大幅度提升防御塔的持续恢复效果,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,2025,,,,Tower,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
117,Skill - ImmunityDamagePeriodicity,LevelUp - ImmunityDamagePeriodicity,防御塔周期性免疫伤害,10,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,2026,,,,Tower,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
118,Skill - IncreaseSummorAttackDamageBySurvivalTimeLevel1,LevelUp - IncreaseSummorAttackDamageBySurvivalTimeLevel1,依据召唤物生存时间，小幅度提升其攻击力,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,1,,,AttributeProperty,survivalTime,,2027,,,,Summor,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
119,Skill - IncreaseSummorAttackDamageBySurvivalTimeLevel2,LevelUp - IncreaseSummorAttackDamageBySurvivalTimeLevel2,依据召唤物生存时间，中幅度提升其攻击力,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,1,,,AttributeProperty,survivalTime,,2028,,,,Summor,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
120,Skill - IncreaseSummorAttackDamageBySurvivalTimeLevel3,LevelUp - IncreaseSummorAttackDamageBySurvivalTimeLevel3,依据召唤物生存时间，大幅度提升其攻击力,3,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,1,,,AttributeProperty,survivalTime,,2029,,,,Summor,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
121,Skill - ExplodeSummorInDeath,LevelUp - ExplodeSummorInDeath,召唤物死亡时会爆炸，对周围敌人造成大量伤害,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,1,,,PlayerController,_validAttackDamage,,,,1,360,Enemy,Damage,aoe,,Sector,,,,TimeOver,,,,,,,,,,
122,Skill - RestoreSummorHealthByKillEnemyLevel1,LevelUp - RestoreSummorHealthByKillEnemyLevel1,附近敌人死亡时，召唤物有几率恢复少量生命,0,0,FollowMove,ConstantProbability,{”probability”:”0.5”},Linear,Constant,1,,,,,,2030,,1,360,Summor,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
123,Skill - RestoreSummorHealthByKillEnemyLevel2,LevelUp - RestoreSummorHealthByKillEnemyLevel2,附近敌人死亡时，召唤物有几率恢复中量生命,0,0,FollowMove,ConstantProbability,{”probability”:”0.5”},Linear,Constant,1,,,,,,2031,,1,360,Summor,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
124,Skill - RestoreSummorHealthByKillEnemyLevel3,LevelUp - RestoreSummorHealthByKillEnemyLevel3,附近敌人死亡时，召唤物有几率恢复大量生命,0,0,FollowMove,ConstantProbability,{”probability”:”0.5”},Linear,Constant,1,,,,,,2032,,1,360,Summor,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
125,Skill - RandomSummonByKillEnemy,LevelUp - RandomSummonByKillEnemy,敌人死亡时，有几率在其死亡处生成一个随机友方召唤物,0,0,FollowMove,ConstantProbability,{”probability”:”0.01”},Linear,Constant,1,,,,,,,Summor/Bat;Summor/DryadSeed;Summor/FrostGiants;Summor/LightingBird,,,,RandomSummon,,,Self,,,,TimeOver,,,,,,,,,,
127,Skill - IncreaseThawDamageLevel1,LevelUp - IncreaseThawDamageLevel1,陷入冰冻状态时，敌人会失去最大生命值的15%(最高为100),10,0,FollowMove,ObtainRequireBuff,{”requiredBuffTypeID”:”1002”},Linear,Constant,0.1,,,AttributeProperty,_validMaxHealth,,,,,,,Damage,,,Self,,,,TimeOver,,,,,,,,,,
128,Skill - IncreaseThawDamageLevel2,LevelUp - IncreaseThawDamageLevel2,陷入冰冻状态时，敌人会失去最大生命值的20%(最高为100),10,0,FollowMove,ObtainRequireBuff,{”requiredBuffTypeID”:”1002”},Linear,Constant,0.2,,,AttributeProperty,_validMaxHealth,,,,,,,Damage,,,Self,,,,TimeOver,,,,,,,,,,
129,Skill - IncreaseThawDamageLevel3,LevelUp - IncreaseThawDamageLevel3,陷入冰冻状态时，敌人会失去最大生命值的30%(最高为100),10,0,FollowMove,ObtainRequireBuff,{”requiredBuffTypeID”:”1002”},Linear,Constant,0.3,,,AttributeProperty,_validMaxHealth,,,,,,,Damage,,,Self,,,,TimeOver,,,,,,,,,,
3001,Skill -GainRandomTowerAtGameStartLevel1,OutSceneTalent -GainRandomTowerAtGameStartLevel1,开局获得一个随机防御塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,Tower/FireTower;Tower/IceTower;Tower/LightTower;Tower/LightningTower;Tower/DarkTower;Tower/WoodTower;Tower/VineTower,,,,RandomSummon,,,Self,,,,TimeOver,,,,,,,,,,
3002,Skill -GainRandomTowerAtGameStartLevel1,OutSceneTalent -GainRandomTowerAtGameStartLevel1,开局获得一个随机防御塔，有小概率获得强化版本,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,Tower/FireTower;Tower/IceTower;Tower/LightTower;Tower/LightningTower;Tower/DarkTower;Tower/WoodTower;Tower/VineTower;Tower/BlizzardTower;Tower/ChainLightningTower;Tower/DarkChargeTower;Tower/DryadTower;Tower/FireBallTower;Tower/FireJetTower,,,,RandomSummon,,,Self,,,,TimeOver,,,,,,,,,,
3003,Skill -GainRandomTowerAtGameStartLevel1,OutSceneTalent -GainRandomTowerAtGameStartLevel1,开局获得一个随机防御塔，有大概率获得强化版本,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,Tower/FireTower;Tower/IceTower;Tower/ChainLightningTower;Tower/DarkChargeTower;Tower/DryadTower;Tower/FireBallTower;Tower/FireJetTower,,,,RandomSummon,,,Self,,,,TimeOver,,,,,,,,,,
3004,Skill - IncreaseTowerAttackIfOnlyOneLiveLevel1,OutSceneTalent - IncreaseTowerAttackIfOnlyOneLiveLevel1,当玩家仅有一座防御塔时，防御塔的攻击力大幅提升,1,0,FollowMove,CheckPropertyValue,"{”script”:”ScenceLog”&""varName"":""TowerAlive""&""compareMethod"":""equal""}",Linear,Constant,1,,,,,,2033,,,,Tower,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
3005,Skill - IncreaseTowerAttackIfOnlyOneLiveLevel2,OutSceneTalent - IncreaseTowerAttackIfOnlyOneLiveLevel2,当玩家仅有一座防御塔时，防御塔的攻击力大幅提升,1,0,FollowMove,CheckPropertyValue,"{”script”:”ScenceLog”&""varName"":""TowerAlive""&""compareMethod"":""equal""}",Linear,Constant,1,,,,,,2034,,,,Tower,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
3006,Skill - IncreaseTowerAttackIfOnlyOneLiveLevel3,OutSceneTalent - IncreaseTowerAttackIfOnlyOneLiveLevel3,当玩家仅有一座防御塔时，防御塔的攻击力大幅提升,1,0,FollowMove,CheckPropertyValue,"{”script”:”ScenceLog”&""varName"":""TowerAlive""&""compareMethod"":""equal""}",Linear,Constant,1,,,,,,2035,,,,Tower,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
3007,Skill - InspireFriendAfterBuildTowerLevel1,OutSceneTalent - InspireFriendAfterBuildTowerLevel1,建造防御塔后，鼓舞周围所有友方角色，小幅度提升友方攻击速度,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,2036,,,,Summor;Tower;Player,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3008,Skill - InspireFriendAfterBuildTowerLevel2,OutSceneTalent - InspireFriendAfterBuildTowerLevel2,建造防御塔后，鼓舞周围所有友方角色，小幅度提升友方攻击速度,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,2037,,,,Summor;Tower;Player,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3009,Skill - InspireFriendAfterBuildTowerLevel3,OutSceneTalent - InspireFriendAfterBuildTowerLevel3,建造防御塔后，鼓舞周围所有友方角色，小幅度提升友方攻击速度,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,2038,,,,Summor;Tower;Player,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3010,Skill - KnockBackEnemyNearByAfterSummonLevel1,OutSceneTalent - KnockBackEnemyNearByAfterSummonLevel1,当玩家主动召唤时，击退周围敌人并造成少量伤害,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,Enemy,Damage,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3011,Skill - KnockBackEnemyNearByAfterSummonLevel1,OutSceneTalent - KnockBackEnemyNearByAfterSummonLevel1,当玩家主动召唤时，击退周围敌人并造成少量伤害,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,Enemy,KnockBack,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3012,Skill - KnockBackEnemyNearByAfterSummonLevel2,OutSceneTalent - KnockBackEnemyNearByAfterSummonLevel2,当玩家主动召唤时，击退周围敌人并造成少量伤害,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,Enemy,Damage,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3013,Skill - KnockBackEnemyNearByAfterSummonLevel2,OutSceneTalent - KnockBackEnemyNearByAfterSummonLevel2,当玩家主动召唤时，击退周围敌人并造成少量伤害,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,Enemy,KnockBack,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3014,Skill - KnockBackEnemyNearByAfterSummonLevel2,OutSceneTalent - KnockBackEnemyNearByAfterSummonLevel2,当玩家主动召唤时，击退周围敌人并造成少量伤害,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,Enemy,Damage,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3015,Skill - KnockBackEnemyNearByAfterSummonLevel2,OutSceneTalent - KnockBackEnemyNearByAfterSummonLevel2,当玩家主动召唤时，击退周围敌人并造成少量伤害,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,Enemy,KnockBack,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3016,Skill - ReduceCDAfterSommonDeathLevel1,OutSceneTalent - ReduceCDAfterSommonDeathLevel1,召唤物死亡时，有小概率缩短玩家的主动技能CD,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,ReduceCoolDown,,,,,,,TimeOver,,,,,,,,,,
3017,Skill - ReduceCDAfterSommonDeathLevel2,OutSceneTalent - ReduceCDAfterSommonDeathLevel2,召唤物死亡时，有小概率缩短玩家的主动技能CD,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,2,,,,,,,,,,,ReduceCoolDown,,,,,,,TimeOver,,,,,,,,,,
3018,Skill - ReduceCDAfterSommonDeathLevel3,OutSceneTalent - ReduceCDAfterSommonDeathLevel3,召唤物死亡时，有小概率缩短玩家的主动技能CD,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,3,,,,,,,,,,,ReduceCoolDown,,,,,,,TimeOver,,,,,,,,,,
3019,Skill - ReduceCDAfterSommonDeathLevel1,OutSceneTalent - ReduceCDAfterSommonDeathLevel1,召唤物击杀敌人时，有小概率拾取附近的经验，并加倍返还给玩家,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,1,360,Drops,AbsorbExpItem,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3020,Skill - ReduceCDAfterSommonDeathLevel2,OutSceneTalent - ReduceCDAfterSommonDeathLevel2,召唤物击杀敌人时，有小概率拾取附近的经验，并加倍返还给玩家,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1.5,,,,,,,,1,360,Drops,AbsorbExpItem,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3021,Skill - ReduceCDAfterSommonDeathLevel3,OutSceneTalent - ReduceCDAfterSommonDeathLevel3,召唤物击杀敌人时，有小概率拾取附近的经验，并加倍返还给玩家,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,2,,,,,,,,1,360,Drops,AbsorbExpItem,aoe,,Sector,,,,TimeOver,,,,,,,,,,
3022,Skill - IncreasPlayerAttackInLowHealthLevel1,OutSceneTalent - IncreasPlayerAttackInLowHealthLevel1,血量低于50%时，获得大幅度攻击力提升,0,0,FollowMove,CheckPropertyValue,"{”script”:”AttributeProperty”&""varName"":""HealthPercentage""&""compareMethod"":""less""&“compareValue”:""0.5""}",Linear,Constant,0.5,,,,,,2039,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
3023,Skill - IncreasPlayerAttackInLowHealthLevel2,OutSceneTalent - IncreasPlayerAttackInLowHealthLevel1,血量低于60%时，获得大幅度攻击力提升,0,0,FollowMove,CheckPropertyValue,"{”script”:”AttributeProperty”&""varName"":""HealthPercentage""&""compareMethod"":""less""&“compareValue”:""0.6""}",Linear,Constant,0.6,,,,,,2040,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
3024,Skill - IncreasPlayerAttackInLowHealthLevel3,OutSceneTalent - IncreasPlayerAttackInLowHealthLevel1,血量低于70%时，获得大幅度攻击力提升,0,0,FollowMove,CheckPropertyValue,"{”script”:”AttributeProperty”&""varName"":""HealthPercentage""&""compareMethod"":""less""&“compareValue”:""0.7""}",Linear,Constant,0.7,,,,,,2041,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
3025,Skill - GainExtraRerollTimesLevel1,OutSceneTalent - GainExtraRerollTimesLevel1,开局时，获得1个额外重置次数,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,ScenceLog,RerollTimes,,,,,,,CoefficientModify,,,,,,,TimeOver,,,,,,,,,,
3026,Skill - GainExtraRerollTimesLevel2,OutSceneTalent - GainExtraRerollTimesLevel2,开局时，获得2个额外重置次数,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,2,,,ScenceLog,RerollTimes,,,,,,,CoefficientModify,,,,,,,TimeOver,,,,,,,,,,
3027,Skill - GainExtraRerollTimesLevel3,OutSceneTalent - GainExtraRerollTimesLevel3,开局时，获得3个额外重置次数,0,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,3,,,ScenceLog,RerollTimes,,,,,,,CoefficientModify,,,,,,,TimeOver,,,,,,,,,,
3028,Skill - GainExtraMaxHealthLevel1,OutSceneTalent - GainExtraMaxHealthLevel1,玩家每击杀1000个敌人，获得一点额外的最大生命,0,0,FollowMove,CheckPropertyValue,"{”script”:”ScenceLog”&""varName"":""EnemyDeath""&""compareMethod"":""divisible""&“compareValue”:""1000""}",Linear,Constant,5,,,,,,2042,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
3029,Skill - GainExtraMaxHealthLevel2,OutSceneTalent - GainExtraMaxHealthLevel2,玩家每击杀800个敌人，获得一点额外的最大生命,0,0,FollowMove,CheckPropertyValue,"{”script”:”ScenceLog”&""varName"":""EnemyDeath""&""compareMethod"":""divisible""&“compareValue”:""800""}",Linear,Constant,4,,,,,,2042,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
3030,Skill - GainExtraMaxHealthLevel3,OutSceneTalent - GainExtraMaxHealthLevel3,玩家每击杀600个敌人，获得一点额外的最大生命,0,0,FollowMove,CheckPropertyValue,"{”script”:”ScenceLog”&""varName"":""EnemyDeath""&""compareMethod"":""divisible""&“compareValue”:""600""}",Linear,Constant,3,,,,,,2042,,,,,AddBuff,,,Self,,,,TimeOver,,,,,,,,,,
4000,Test - UnlockStandardTower,Test - UnlockStandardTower,测试-解锁建造标准防御塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,4001,,,,4000,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
4001,StandardTower,Test - UnlockStandardTower,召唤一座标准防御塔,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
4002,Test - UnlockStandardSummor,Test - UnlockStandardSummor,测试-解锁召唤标准召唤物,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,4003,,,,4002,,,,,,,UnlockPlayerSkill,,,Self,,,,TimeOver,,,,,,,,,,
4003,StandardSummor,Test - UnlockStandardSummor,召唤标准召唤物,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,,,,,,,,TimeOver,,,,,,,,,,
5000,Enemy - Dash,Enemy - Dash,沿某个方向冲刺，摧毁沿途的防御塔，击飞召唤物和玩家,10,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,1,,,Enemy,_validAttackDamage,,,,,,,Dash,,,Player,,,,TimeOver,,,,,,,,,,
5001,Enemy - LaserCannon,Enemy - LaserCannon,先在地面上生成方向指示器，然后发射一道激光，沿途所有物体受到大量伤害,10,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,1,,,Enemy,_validAttackDamage,,,,,1,,LaserCannon,,,Player,,,,TimeOver,,,,,,,,,,
5002,Enemy - DeathAbsorb,Enemy - DeathAbsorb,每一个敌方死亡时，增加移动速度和攻击力,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,5002,,,,,DeathAbsorb,,,Self,,,,TimeOver,,,,,,,,,,
5003,Enemy - ExplodeArea,Enemy - ExplodeArea,附近敌人死亡时爆炸并对范围内友方造成大量伤害,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,5000,,1,360,Enemy,ExplodeArea,,,Sector,,,,TimeOver,,,,,,,,,,
5004,Enemy - CurseArea,Enemy - CurseArea,以自身为中心圆形范围内的敌对目标缓慢掉血，伤害受到攻击力加成,-1,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,1,,,Enemy,_validAttackDamage,,,,1,360,Summor;Tower;Player,Damage,aoe,,Sector,,,,TimeOver,,,,,,,,,,
5005,Enemy - Bilocation,Enemy - Bilocation,长蓄力动画，召唤2个具有本体血量20%的分身，分身无法使用技能,100,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,2,,,,,,,,,,,Bilocation,,,,,,,TimeOver,,,,,,,,,,
5006,Enemy - SummonMeteors,Enemy - SummonMeteors,召唤一堆陨石，先在地面上生成落点标记，虽然对落点处的物体造成大量伤害,10,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,5,,,,,,,,,,,SummonMeteors,,,,,,,TimeOver,,,,,,,,,,
5007,Enemy - Recover,Enemy - Recover,停留在原地并开始回血，期间不能移动但可以释放其他技能,20,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.3,,,Enemy,_validMaxHealth,,,,,,,Recover,,,,,,,TimeOver,,,,,,,,,,
5008,Enemy - Roar,Enemy - Roar,以boss为中心生成一个范围指示器，然后减速玩家和召唤物，并使防御塔短暂失效,20,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.3,,,Enemy,_validAttackDamage,,,,,,,Roar,,,,,,,TimeOver,,,,,,,,,,
5009,Enemy - ShakeGround,Enemy - ShakeGround,以boss为中心生成一个范围指示器，然后对范围内所有生物造成大量伤害，摧毁建筑，并且该范围一段时间内无法再建造建筑，路过该范围的友方会被减速,20,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.3,,,Enemy,_validAttackDamage,,,,,,,ShakeGround,,,,,,,TimeOver,,,,,,,,,,
5010,Enemy - Furious,Enemy - Furious,大幅增加自身的移动速度，生命回复和攻击力,20,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.3,,,Enemy,_validAttackDamage,,,,,,,Furious,,,,,,,TimeOver,,,,,,,,,,
5011,Enemy - SummonWhirlwind,Enemy - SummonWhirlwind,召唤3道环绕在身边的旋风，旋风对接触的生物造成大量伤害,20,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Property,0.3,,,Enemy,_validAttackDamage,,,,,,,SummonWhirlwind,,,,,,,TimeOver,,,,,,,,,,
5012,Enemy - ReinforcementFriendlyForces,Enemy - ReinforcementFriendlyForces,全体友方加攻击和移速，并缓慢恢复生命,10,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,5001,,1,360,Enemy,AddBuff,aoe,,Sector,,,,TimeOver,,,,,,,,,,
5013,Enemy - OpenPortal,Enemy - OpenPortal,先在地面上生成3个指示器，然后摧毁指示器位置上的建筑，每个指示器生成一个传送门，传送门持续20s，会不停召唤敌方单位，传送门可以被提前摧毁,10,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,OpenPortal,,,,,,,TimeOver,,,,,,,,,,
5014,Enemy - SummonChaser,Enemy - SummonChaser,缓慢召唤动画，召唤数个追踪玩家的追踪弹，对碰触的物体造成大量伤害,10,0,FollowMove,ConstantProbability,{”probability”:”1”},Linear,Constant,1,,,,,,,,,,,SummonChaser,,,,,,,TimeOver,,,,,,,,,,
6000,Derivative - ExplodeInDeath,Derivative - ExplodeInDeath,死亡时爆炸,-1,0,FollowMove,ObtainRequireBuff,{”requiredBuffTypeID”:”5000”},Linear,Constant,1,,,,,,,,1,360,Summor;Tower;Player,Damage,aoe,,Sector,,,,TimeOver,,,,,,,,,,
