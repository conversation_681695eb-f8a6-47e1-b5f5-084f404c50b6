{"version": 3, "targets": {".NETStandard,Version=v2.1": {"NaughtyAttributes.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/NaughtyAttributes.Core.dll": {}}, "runtime": {"bin/placeholder/NaughtyAttributes.Core.dll": {}}}}}, "libraries": {"NaughtyAttributes.Core/1.0.0": {"type": "project", "path": "NaughtyAttributes.Core.csproj", "msbuildProject": "NaughtyAttributes.Core.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["NaughtyAttributes.Core >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\NaughtyAttributes.Editor.csproj", "projectName": "NaughtyAttributes.Editor", "projectPath": "h:\\Works\\TS\\NaughtyAttributes.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\NaughtyAttributes.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"h:\\Works\\TS\\NaughtyAttributes.Core.csproj": {"projectPath": "h:\\Works\\TS\\NaughtyAttributes.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}}