{"format": 1, "restore": {"h:\\Works\\TS\\Assembly-CSharp.csproj": {}}, "projects": {"h:\\Works\\TS\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "h:\\Works\\TS\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"h:\\Works\\TS\\AstarPathfindingProject.csproj": {"projectPath": "h:\\Works\\TS\\AstarPathfindingProject.csproj"}, "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj": {"projectPath": "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj"}, "h:\\Works\\TS\\NaughtyAttributes.Core.csproj": {"projectPath": "h:\\Works\\TS\\NaughtyAttributes.Core.csproj"}, "h:\\Works\\TS\\NaughtyAttributes.Editor.csproj": {"projectPath": "h:\\Works\\TS\\NaughtyAttributes.Editor.csproj"}, "h:\\Works\\TS\\NaughtyAttributes.Test.csproj": {"projectPath": "h:\\Works\\TS\\NaughtyAttributes.Test.csproj"}, "h:\\Works\\TS\\PackageToolsEditor.csproj": {"projectPath": "h:\\Works\\TS\\PackageToolsEditor.csproj"}, "h:\\Works\\TS\\Unity.RenderPipelines.HighDefinition.Config.Runtime.csproj": {"projectPath": "h:\\Works\\TS\\Unity.RenderPipelines.HighDefinition.Config.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}, "h:\\Works\\TS\\AstarPathfindingProject.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\AstarPathfindingProject.csproj", "projectName": "AstarPathfindingProject", "projectPath": "h:\\Works\\TS\\AstarPathfindingProject.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\AstarPathfindingProject\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}, "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj", "projectName": "AstarPathfindingProjectEditor", "projectPath": "h:\\Works\\TS\\AstarPathfindingProjectEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\AstarPathfindingProjectEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"h:\\Works\\TS\\AstarPathfindingProject.csproj": {"projectPath": "h:\\Works\\TS\\AstarPathfindingProject.csproj"}, "h:\\Works\\TS\\PackageToolsEditor.csproj": {"projectPath": "h:\\Works\\TS\\PackageToolsEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}, "h:\\Works\\TS\\NaughtyAttributes.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\NaughtyAttributes.Core.csproj", "projectName": "NaughtyAttributes.Core", "projectPath": "h:\\Works\\TS\\NaughtyAttributes.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\NaughtyAttributes.Core\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}, "h:\\Works\\TS\\NaughtyAttributes.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\NaughtyAttributes.Editor.csproj", "projectName": "NaughtyAttributes.Editor", "projectPath": "h:\\Works\\TS\\NaughtyAttributes.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\NaughtyAttributes.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"h:\\Works\\TS\\NaughtyAttributes.Core.csproj": {"projectPath": "h:\\Works\\TS\\NaughtyAttributes.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}, "h:\\Works\\TS\\NaughtyAttributes.Test.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\NaughtyAttributes.Test.csproj", "projectName": "NaughtyAttributes.Test", "projectPath": "h:\\Works\\TS\\NaughtyAttributes.Test.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\NaughtyAttributes.Test\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"h:\\Works\\TS\\NaughtyAttributes.Core.csproj": {"projectPath": "h:\\Works\\TS\\NaughtyAttributes.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}, "h:\\Works\\TS\\PackageToolsEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\PackageToolsEditor.csproj", "projectName": "PackageToolsEditor", "projectPath": "h:\\Works\\TS\\PackageToolsEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\PackageToolsEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"h:\\Works\\TS\\AstarPathfindingProject.csproj": {"projectPath": "h:\\Works\\TS\\AstarPathfindingProject.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}, "h:\\Works\\TS\\Unity.RenderPipelines.HighDefinition.Config.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\Unity.RenderPipelines.HighDefinition.Config.Runtime.csproj", "projectName": "Unity.RenderPipelines.HighDefinition.Config.Runtime", "projectPath": "h:\\Works\\TS\\Unity.RenderPipelines.HighDefinition.Config.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\Unity.RenderPipelines.HighDefinition.Config.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}}}