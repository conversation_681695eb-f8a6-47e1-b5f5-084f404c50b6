using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices.ComTypes;
using UnityEngine;
using UnityEngine.InputSystem;

public class PropertyCoefficientGenerator : ICoefficientGenerator
{
    public static Component GetComponentByTypeName(GameObject gameObject, string scriptType)
    {
        //依照scriptName生成type
        var type = System.Type.GetType(scriptType);
        //获取gameObject上的指定类型的脚本
        var script = gameObject.GetComponent(type);
        return script;
    }
    public float GenerateCoefficient(SkillData data, Transform skillTF)
    {
        Component script;
        if (data.scriptName == scriptType.ScenceLog)
        {
            script = ScenceLog.Instance;
        }
        else
        {
            script = GetComponentByTypeName(data.owner.gameObject, data.scriptName.ToString());
        }

        //调用脚本上的GetPropertyValue方法
        System.Reflection.MethodInfo method = script.GetType().GetMethod("GetPropertyValue");
        if (method == null)
        {
            Debug.LogError("未找到GetPropertyValue方法");
            return 0;
        }
        object value = method.Invoke(script, new object[] { data.propertyField });
        //获取属性值并尝试转换为float
        float propertyValue = float.Parse(value.ToString());
        if (float.IsNaN(propertyValue))
        {
            Debug.LogError("属性值转换为float失败");
            return 0;
        }

        //获取加成器
        IAttenuationFormula attenuationFormula = DeployerConfigFactory.CreateAttenuationFormula(data);
        return attenuationFormula.Attenuation((float)propertyValue * data.attackCoeffient);
    }
}
