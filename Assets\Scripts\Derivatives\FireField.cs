using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class FireField : MonoBehaviour
{
    private float _maxGapTime = 5.0f;
    private float _gapTime;
    private float _lifeTime = 5.0f;
    private BuffData _buffData;
    private LayerMask _layerMask;
    // Start is called before the first frame update
    void Start()
    {
        _gapTime = 0;
        _buffData = Resources.Load("Data/BuffData/1000") as BuffData;
        _layerMask = _buffData.Layers();

    }

    // Update is called once per frame
    void Update()
    {
        _gapTime = Mathf.Clamp(_gapTime - Time.deltaTime, 0, _maxGapTime);
        _lifeTime -= Time.deltaTime;
        if (_lifeTime <= 0)
        {
            Destroy(gameObject);
        }
    }

    private void SetFire(Collider2D collider)
    {
        BuffManager buffManager = collider.transform.GetComponent<BuffManager>();
        if (buffManager != null)
        {
            buffManager.AddBuff(_buffData);
        }
    }
    private void OnTriggerEnter2D(Collider2D collider)
    {
        //_layerMask与collider.gameObject.layer的位与运算，判断碰撞体是否在buffData的layerMask中
        //运算方式为按位与，如果有至少一个位是1，则结果为1，否则为0

        bool isInLayerMask = (_layerMask.value & (int)Mathf.Pow(2, collider.gameObject.layer)) == (int)Mathf.Pow(2, collider.gameObject.layer);
        //如果碰撞体在buffData的layerMask中
        if (isInLayerMask & _gapTime <= 0)
        {
            SetFire(collider);
        }
    }

    public void SetAttackGap(float maxGapTime)
    {
        _maxGapTime = maxGapTime;
    }

    //在Gizmos中标识攻击范围
    void OnDrawGizmos()
    {
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, transform.GetComponent<Renderer>().bounds.size.x / 2);
    }
}

