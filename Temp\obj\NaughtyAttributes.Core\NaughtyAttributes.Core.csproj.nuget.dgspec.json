{"format": 1, "restore": {"h:\\Works\\TS\\NaughtyAttributes.Core.csproj": {}}, "projects": {"h:\\Works\\TS\\NaughtyAttributes.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "h:\\Works\\TS\\NaughtyAttributes.Core.csproj", "projectName": "NaughtyAttributes.Core", "projectPath": "h:\\Works\\TS\\NaughtyAttributes.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "h:\\Works\\TS\\Temp\\obj\\\\NaughtyAttributes.Core\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}}}